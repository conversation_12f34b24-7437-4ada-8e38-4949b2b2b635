<!-- 现场信息反馈 -->
<view class="container">
  <!-- 任务单信息 -->
  <view class="task-info" wx:if="{{currentTask}}">
    <view class="info-header">
      <view class="header-left">
        <view class="project-title">
          <text class="project-name">工程名称：{{currentTask.project_name}}</text>
          <text class="project-code" wx:if="{{currentTask.project_code}}">工程编号：{{currentTask.project_code}}</text>
        </view>
      </view>
      <view class="switch-task" bindtap="onShowTaskSelector">
        <text class="switch-text">切换任务单</text>
      </view>
    </view>
    <view class="task-details">
      <text class="task-number">任务单号：{{currentTask.task_number}}</text>
      <text class="task-number">强度等级：{{currentTask.strength_grade}}</text>
      <text class="part-name">部位名称：{{currentTask.part_name}}</text>
      <text class="task-desc">施工单位：{{currentTask.construction_unit}}</text>
      <text class="task-time">计划时间：{{currentTask.scheduled_time_text}}</text>
      <view class="task-status">
        <text class="status-label">状态：</text>
        <text class="status-tag status-{{currentTask.supply_status}}">
          {{currentTask.supply_status === 'supplying' ? '正供' : '供毕'}}
        </text>
      </view>
    </view>
  </view>
  <!-- 现场信息反馈表单 -->
  <view class="feedback-form">
    <!-- 反馈类别 -->
    <view class="form-section">
      <text class="form-label">反馈类别</text>
      <view class="category-selector" bindtap="onShowCategorySelector">
        <text class="category-text">{{feedbackForm.category || '请选择反馈类别'}}</text>
        <text class="category-arrow">▼</text>
      </view>
    </view>
    <view class="form-section">
      <text class="form-label">情况描述</text>
      <textarea class="form-textarea" placeholder="请输入情况描述" value="{{feedbackForm.notes}}" bindinput="onNotesInput"></textarea>
    </view>
    <!-- 上传 -->
    <view class="media-section">
      <!-- 拍照 -->
      <view class="media-group">
        <text class="media-label">拍照</text>
        <view class="media-actions">
          <button class="btn btn-secondary" bindtap="onOpenCamera" data-mode="photo">拍摄照片</button>
        </view>
        <!-- 拍摄的照片预览 -->
        <view class="image-list" wx:if="{{images.length > 0}}">
          <view class="image-item" wx:for="{{images}}" wx:key="index">
            <image
              src="{{item}}"
              mode="aspectFit"
              bindtap="onPreviewImage"
              data-src="{{item}}"
              class="preview-image"
            ></image>

          </view>
        </view>
      </view>
      <!-- 录像 -->
      <view class="media-group">
        <view class="media-header">
          <text class="media-label">录像</text>
          <text class="duration-limit" wx:if="{{videoDurationSettings}}">
            时长限制: {{videoDurationSettings.min_duration}}-{{videoDurationSettings.max_duration}}秒
          </text>
          <text class="duration-limit loading" wx:else>加载中...</text>
        </view>
        <view class="media-actions">
          <button class="btn btn-secondary" bindtap="onOpenCamera" data-mode="video">录制视频</button>
        </view>
        <!-- 录制的视频预览 -->
        <view class="video-list" wx:if="{{videos.length > 0}}">
          <view class="video-item" wx:for="{{videos}}" wx:key="index">
            <video
              src="{{item.src}}"
              controls
              class="preview-video"
              object-fit="contain"
              direction="0"
              show-fullscreen-btn="{{false}}"
              show-play-btn="{{true}}"
              show-center-play-btn="{{true}}"
            ></video>

            <view class="video-info">
              <text class="video-duration">时长: {{item.duration}}秒</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 录音 -->
      <view class="media-group">
        <text class="media-label">录音</text>
        <view class="record-controls">
          <button class="btn btn-{{recording ? 'danger' : 'secondary'}}" bindtap="onToggleRecord">
            {{recording ? '停止录音' : '开始录音'}}
          </button>
          <text class="record-time" wx:if="{{recording}}">{{recordTime}}s</text>
        </view>
        <view class="audio-list" wx:if="{{audios.length > 0}}">
          <view class="audio-item {{playingStates[index] ? 'playing' : ''}}" wx:for="{{audios}}" wx:key="index">
            <view class="audio-content">
              <view class="audio-icon">
                <text class="audio-wave {{playingStates[index] ? 'active' : ''}}">🎵</text>
              </view>
              <view class="audio-info">
                <view class="audio-header">
                  <text class="audio-name">录音{{index + 1}}</text>
                  <text class="audio-status" wx:if="{{playingStates[index]}}">播放中</text>
                </view>
                <text class="audio-duration">时长：{{item.duration}}秒</text>
              </view>
            </view>
            <view class="audio-controls">
              <button class="audio-btn play-btn {{playingStates[index] ? 'playing' : ''}}" bindtap="onPlayAudio" data-index="{{index}}">
                <text class="btn-icon">{{playingStates[index] ? '■' : '▶'}}</text>
                <text class="btn-text">{{playingStates[index] ? '暂停' : '播放'}}</text>
              </button>
            </view>

          </view>
        </view>
      </view>
    </view>
    <!-- 记录人员 -->
    <view class="form-section">
      <text class="form-section">记录人员</text>
      <view class="feedback-user-display">{{userInfo.PersonName}}</view>
    </view>
    <!-- 反馈时间 -->
    <view class="form-section">
      <text class="form-section">反馈时间</text>
      <view class="time-display">{{feedbackForm.feedback_time}}</view>
    </view>
    <!-- 反馈位置 -->
    <view class="form-section">
      <text class="form-section">反馈位置</text>
      <view class="location-container">
        <view class="location-display {{feedbackForm.location_status}}">
          <text wx:if="{{feedbackForm.location_status === 'authorized'}}">{{feedbackForm.location_desc}}</text>
          <text wx:elif="{{feedbackForm.location_status === 'denied'}}" class="location-denied">{{feedbackForm.location_desc}}</text>
          <text wx:else class="location-unavailable">位置信息不可用</text>
        </view>

        <!-- 小地图显示 -->
        <view class="mini-map-container" wx:if="{{feedbackForm.location_status === 'authorized' && feedbackForm.latitude && feedbackForm.longitude}}">
          <map
            id="feedbackMap"
            class="mini-map"
            longitude="{{feedbackForm.longitude}}"
            latitude="{{feedbackForm.latitude}}"
            scale="16"
            markers="{{mapMarkers}}"
            show-location="{{false}}"
            enable-zoom="{{true}}"
            enable-scroll="{{true}}"
            enable-rotate="{{false}}"
            enable-satellite="{{false}}"
            enable-traffic="{{false}}"
            bindtap="onMapTap"
          ></map>
        </view>

        <!-- 重新获取位置按钮 -->
        <view class="refresh-button" bindtap="refreshAccurateLocation">
          <text class="refresh-button-text">刷新位置</text>
        </view>
      </view>
    </view>
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="btn btn-primary submit-btn" bindtap="onSubmit" disabled="{{submitting}}">
        {{submitting ? '提交中...' : '提 交'}}
      </button>
      <!-- 最基础测试按钮 -->
      <button class="btn btn-secondary" bindtap="basicTest" style="margin-top: 10px; background-color: #28a745;">
        基础测试
      </button>
      <!-- 简化版提交按钮 -->
      <button class="btn btn-warning" bindtap="simpleSubmit" disabled="{{submitting}}" style="margin-top: 10px; background-color: #ff9500;">
        简化提交(无网络)
      </button>
      <!-- 原测试按钮 -->
      <button class="btn btn-secondary" bindtap="testSubmit" style="margin-top: 10px;">
        原测试按钮
      </button>
    </view>
  </view>
  <!-- 反馈类别选择器弹窗 -->
  <view class="modal" wx:if="{{showCategorySelector}}" bindtap="onCloseCategorySelector">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">选择反馈类别</text>
        <text class="modal-close" bindtap="onCloseCategorySelector">×</text>
      </view>
      <view class="modal-body">
        <view class="category-list">
          <view class="category-item {{feedbackForm.category === item ? 'selected' : ''}}" wx:for="{{feedbackCategories}}" wx:key="*this" bindtap="onSelectCategory" data-category="{{item}}">
            <text class="category-name">{{item}}</text>
            <text class="category-check" wx:if="{{feedbackForm.category === item}}">✓</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 任务单选择器弹窗 -->
  <view class="modal" wx:if="{{showTaskSelector}}" bindtap="onCloseTaskSelector">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">选择任务单（当前工程）</text>
        <text class="modal-close" bindtap="onCloseTaskSelector">×</text>
      </view>
      <view class="modal-body">
        <view class="task-list">
          <view class="task-item" wx:for="{{tasks}}" wx:key="id" bindtap="onSelectTask" data-task="{{item}}">
            <view class="task-info">
              <text class="task-number">任务单号：{{item.task_number}}</text>
              <text class="task-part">部位名称：{{item.part_name}}</text>
              <view class="project-info">
                <text class="task-project">工程名称：{{item.project_name}}</text>
                <text class="project-code" wx:if="{{item.project_code}}">工程编号：{{item.project_code}}</text>
              </view>
              <text class="task-construction">施工单位：{{item.construction_unit}}</text>
            </view>
            <view class="task-status">
              <text class="status-tag">{{item.supply_status === 'supplying' ? '正供' : '供毕'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

